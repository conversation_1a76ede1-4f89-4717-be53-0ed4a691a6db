import { fileURLToPath, URL } from 'node:url'
import process from 'process'

import { defineConfig, loadEnv } from 'vite'
import vue from '@vitejs/plugin-vue'
import vueJsx from '@vitejs/plugin-vue-jsx'
import vueDevTools from 'vite-plugin-vue-devtools'
import autoImport from 'unplugin-auto-import/vite'
import Components from 'unplugin-vue-components/vite'
import { VantResolver } from '@vant/auto-import-resolver'
import VueRouter from 'unplugin-vue-router/vite'
import UnoCSS from 'unocss/vite'
// https://vite.dev/config/
export default defineConfig(({ mode, command }) => {
  const env = loadEnv(mode, process.cwd())
  return {
    css: {
      preprocessorOptions: {
        scss: {
          silenceDeprecations: ['legacy-js-api'],
          api: 'modern-compiler', // or "modern", "legacy"
        },
        sass: {
          silenceDeprecations: ['legacy-js-api'],
          api: 'modern-compiler', // or "modern", "legacy"
        },
      },
    },
    plugins: [
      VueRouter({
        dts: true,
      }),
      vue(),
      vueJsx(),
      vueDevTools(),
      autoImport({
        dirs: ['src/stores', 'src/composables', 'src/utils'],
        dts: true,
        resolvers: [VantResolver()],
        imports: [
          'vue',
          'vue-router',
          'pinia',
          '@vueuse/core',
          {
            '@vueuse/router': ['useRouteHash', 'useRouteParams', 'useRouteQuery'],
            '@/plugins/request.js': [['default', 'request']],
            'alova/client': [
              'useRequest',
              'useWatcher',
              'useFetcher',
              'usePagination',
              'useForm',
              'useCaptcha',
            ],
            dayjs: [['default', 'dayjs']],
          },
        ],
        eslintrc: {
          enabled: true,
        },
      }),
      Components({
        dirs: ['src/components'],
        resolvers: [VantResolver()],
        directoryAsNamespace: true,
        dts: true,
      }),
      UnoCSS(),
    ],
    resolve: {
      alias: {
        '@': fileURLToPath(new URL('./src', import.meta.url)),
        lodash: 'lodash-es',
      },
    },
    server: {
      // port: 4202, // 服务端口
      open: false, // 是否自动打开浏览器
      host: '0.0.0.0',
      proxy: {
        // 代理
        [env.VITE_APP_BASE_API]: {
          target: env.VITE_APP_PROXY_TARGET,
          ws: false,
          changeOrigin: true,
          rewrite: (p) => p.replace(new RegExp(`^${env.VITE_APP_BASE_API}`), ''),
        },
      },
      cors: true,
    },
  }
})
