export const useAppStore = defineStore('app', () => {
  const name = ref('轻享花')
  const authStore = useAuthStore()

  const { state: env } = useAsyncState(
    () => appJs.getEnv(),
    {},
    {
      shallow: false,
    },
  )

  const h5 = computed(() => env.value?.h5 ?? false)
  const { state: appChannel } = useAsyncState(() => appJs.getAppChannel(), 'VERIFY')
  const { state: appVersion } = useAsyncState(() => appJs.getAppVersion(), '0.0.0')

  const channel = computed(() => {
    if (authStore.user?.creditIntentionFlag === 'Y') {
      return 'OFFICIAL'
    }
    return 'VERIFY'
  })

  return { h5, name, channel, appVersion, appChannel }
})
