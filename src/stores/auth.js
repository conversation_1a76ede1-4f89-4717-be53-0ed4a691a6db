import { getUserInfo } from '@/api/auth'
export const useAuthStore = defineStore(
  'auth',
  () => {
    const from = ref(null)
    const accessToken = ref('')
    const refreshToken = ref('')

    const {
      data: user,
      send: refresh,
      loading,
      error,
    } = useWatcher(
      () => {
        const req = getUserInfo()
        req.config.shareRequest = true
        return req
      },
      [accessToken],
      {
        initialData: null,
        async middleware(_, next) {
          if (!accessToken.value) return
          // await new Promise((resolve) => setTimeout(resolve, 100))
          return next()
        },
      },
    )

    return { from, accessToken, user, refreshToken, refresh, loading, error }
  },
  {
    persist: {
      key: 'qxhua-auth',
      pick: ['accessToken'],
      async afterHydrate(ctx) {},
    },
  },
)
