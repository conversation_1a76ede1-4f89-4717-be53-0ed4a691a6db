export const login = (data) =>
  request.Post(
    '/gateway',
    {
      apiCode: 'BA003',
      ...data,
    },
    {
      name: 'login',
      meta: {
        authRole: 'login',
      },
    },
  )

export const sendCode = (data) =>
  request.Post(
    '/gateway',
    {
      apiCode: 'BA002',
      ...data,
    },
    {
      name: 'sendCode',
      meta: {
        authRole: null,
        raw: true,
      },
    },
  )

export const getUserInfo = (data) =>
  request.Post(
    '/gateway',
    {
      apiCode: 'SF003',
      ...data,
    },
    {
      name: 'getUserInfo',
      meta: {},
    },
  )

export const logout = (data) =>
  request.Post(
    '/gateway',
    {
      apiCode: '',
      ...data,
    },
    {
      name: 'logout',
      meta: {
        authRole: 'logout',
      },
    },
  )
