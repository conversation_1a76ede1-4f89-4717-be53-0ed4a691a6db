export const getNavMenuList = () =>
  request.Post(
    '/gateway',
    {
      apiCode: 'BA015',
    },
    {
      name: 'getNavMenuList',
      meta: {
        authRole: null,
      },
    },
  )

import { getCredit } from './credit'
import dayjs from 'dayjs'
import isBetween from 'dayjs/plugin/isBetween'
dayjs.extend(isBetween)

export const getAdvertisement = (data) =>
  request.Post(
    '/gateway',
    {
      apiCode: 'BA006',
      ...data,
    },
    {
      name: 'getAdInfo',
      meta: {
        authRole: null,
      },
      async transform(data, headers) {
        const authStore = useAuthStore()
        let creditInfo = null
        if (authStore.accessToken) {
          const creditRequest = getCredit({
            sceneCode: 'CONSUME',
          })
          creditRequest.config.shareRequest = true
          creditInfo = await creditRequest
        }
        const { user } = authStore
        // 过滤广告
        Object.entries(data).forEach(([key, value]) => {
          data[key] = value.filter((v) => {
            const remark = (() => {
              try {
                return JSON.parse(v.remark)
              } catch (error) {
                return {}
              }
            })()
            const { isLogin, isVIP, creditIntentionFlag, creditStatus, orderStatus, times } = remark
            if (isLogin) {
              if (isLogin !== (user?.id ? 'Y' : 'N')) {
                return false
              }
            }
            if (creditIntentionFlag) {
              if (creditIntentionFlag !== user?.creditIntentionFlag) {
                return false
              }
            }
            if (creditStatus?.length) {
              if (!user?.id) {
                return false
              }
              if (!creditStatus.includes(creditInfo?.creditStatus)) {
                return false
              }
            }
            if (times?.length) {
              const current = dayjs()
              const v = times.some((time) => {
                if (!time) return false
                return current.isBetween(
                  dayjs(current.format('YYYY-MM-DD') + ' ' + time[0]),
                  dayjs(current.format('YYYY-MM-DD') + ' ' + time[1]),
                )
              })
              if (!v) return false
            }
            return true
          })
        })

        return data
      },
    },
  )

export const getFuncRegion = (data) =>
  request.Post(
    '/gateway',
    {
      apiCode: 'MM004',
      ...data,
    },
    {
      name: 'getFuncRegion',
      meta: {
        authRole: null,
      },
    },
  )

export const getConfigList = (data) =>
  request.Post(
    '/gateway',
    {
      apiCode: 'BA019',
      ...data,
    },
    {
      name: 'getConfig',
      meta: {
        authRole: null,
      },
    },
  )

export const getConfig = (key, returnValue = true) => {
  const m = getConfigList({ key })
  m.config.transform = (data) => (returnValue ? data?.[0]?.configValue : data?.[0])
  return m
}

export const getHotlinePhone = () => getConfig('HOTLINE_PHONE')
