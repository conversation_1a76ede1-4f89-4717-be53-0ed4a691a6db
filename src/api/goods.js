export const getGoodsList = (data) =>
  request.Post(
    '/gateway',
    {
      apiCode: 'MS001',
      ...data,
    },
    {
      name: 'getGoodsList',
      meta: {
        // authRole: null,
        raw: true,
      },
    },
  )
export const getGoodsCategories = (data) =>
  request.Post(
    '/gateway',
    {
      apiCode: 'MS003',
      ...data,
    },
    {
      name: 'getGoodsCategories',
      meta: {
        // authRole: null,
      },
    },
  )

export const getRegionGoodsList = (data) =>
  request.Post(
    '/gateway',
    {
      apiCode: 'MM003',
      ...data,
    },
    {
      name: 'getRegionGoodsList',
      meta: {
        // authRole: null,
        raw: true,
      },
    },
  )
