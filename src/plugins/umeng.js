import { useScriptTag } from '@vueuse/core'

const ID = 1281418232

window._czc = window._czc || []

useScriptTag(`https://s4.cnzz.com/z.js?id=${ID}&async=1`, (el) => {}, {
  // async: true,
})
// ;(function () {
//   var um = document.createElement('script')
//   um.src = `https://s4.cnzz.com/z.js?id=${ID}&async=1`
//   var s = document.getElementsByTagName('script')[0]
//   s.parentNode.insertBefore(um, s)
// })()

// router.isReady().then(() => {
//   const { channel } = router.currentRoute.value?.query
//   if (channel) {
//     window._czc.push(['_setCustomVar', 'channel', channel, 2])
//   }
// })

export function push(...args) {
  window._czc.push([...args])
}
export function setAccount(siteid) {
  push('_setAccount', siteid)
}
export function setAutoPageview(autoPageview = false) {
  push('_setAutoPageview', autoPageview)
}
export function trackEvent(category = '', action = '', label = '', value = 1) {
  push('_trackEvent', category, action, label, value)
}
export function trackPageview(contentUrl, refererUrl = '') {
  push('_trackPageview', contentUrl, refererUrl)
}
export function setUUid(id) {
  push('_setUUid', id)
}

// export default window._czc
