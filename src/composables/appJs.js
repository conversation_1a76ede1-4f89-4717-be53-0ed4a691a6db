import '@/plugins/uni.webview.1.5.6.js'

const uni = window.uni

export const eventTarget = new EventTarget()
;[
  'getAppChannel',
  'oneKeyLoginResponse',
  'appVersion',
  'getPhoneInfo',
  'getLocationInfo',
  'payClient',
].forEach((key) => {
  window[key] = (data) => {
    eventTarget.dispatchEvent(new CustomEvent(key, { detail: data }))
  }
})

async function getEnv() {
  return new Promise((resolve) => {
    uni.getEnv(resolve)
  })
}

async function postMessage(options) {
  const { action, data, timeout = 0, eventName = action } = options

  const out = new Promise(
    (resolve, reject) => timeout > 0 && setTimeout(reject, timeout, new Error('超时')),
  )
  const res = new Promise((resolve) => {
    eventTarget.addEventListener(
      eventName,
      (event) => {
        // console.log(event.detail)
        resolve(event.detail)
      },
      {
        once: true,
      },
    )
    uni.postMessage({
      data: {
        action,
        ...data,
      },
    })
  })
  return Promise.race([out, res])
}

async function getAppChannel() {
  const { h5 } = await getEnv()
  if (h5) {
    return 'OFFICIAL'
  }
  if (ua.os.is('ios')) {
    return 'OFFICIAL'
  }
  return postMessage({ action: 'getAppChannel', timeout: 5000 })
}

async function getAppVersion() {
  const { h5 } = await getEnv()
  if (h5) {
    return '0.0.0'
  }

  return postMessage({ action: 'appVersion', timeout: 5000 })
}

const appJs = {
  getEnv,
  getAppChannel,
  getAppVersion,
}

export default appJs
