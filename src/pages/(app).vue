<template>
  <!-- <div> -->
  <!-- <section class="app"> -->
  <section class="app size-screen h-dvh w-dvw">
    <AppRouterView
      ref="pageView"
      class=""
      :style="{
        '--tabber-height': `${tabbar?.visible ? (tabbar?.height ?? 0) : 0}px`,
      }"
    />
    <AppTabBar ref="tabbar" />
  </section>
</template>

<script setup>
const route = useRoute()
const tabbar = useTemplateRef('tabbar')
useTitle(() => route.meta?.title ?? '')

function refreshTabBar() {
  tabbar.value?.refresh()
}
provide('refreshTabBar', refreshTabBar)
</script>

<style lang="scss" scoped>
.app {
}
</style>
