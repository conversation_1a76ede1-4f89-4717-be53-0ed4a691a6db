<template>
  <Page>
    <template #header>
      <van-nav-bar title="登录" safe-area-inset-top />
    </template>

    <van-form ref="formRef" @submit="submit()">
      <van-cell-group inset>
        <van-field
          v-model="form.phone"
          name="phone"
          type="text"
          label="手机号"
          placeholder="手机号"
          :rules="[
            { required: true, message: '请填写手机号' },
            { pattern: /^1[3456789]\d{9}$/, message: '请填写正确的手机号码' },
          ]"
        />
        <van-field
          v-model="form.code"
          type="text"
          name="code"
          label="验证码"
          placeholder="验证码"
          :rules="[{ required: true, message: '请填写验证码' }]"
        >
          <template #button>
            <van-button
              size="small"
              type="primary"
              :loading="sendingCode"
              :disabled="countdown > 0"
              class="send-code-btn"
              @click="sendCode()"
            >
              <span v-if="countdown > 0"> {{ countdown }}s </span>
              <span v-else>发送验证码</span>
            </van-button>
          </template>
        </van-field>
      </van-cell-group>
      <div style="margin: 16px">
        <van-button :loading="submitting" round block type="primary" native-type="submit">
          提交
        </van-button>
      </div>
    </van-form>
  </Page>
</template>

<script setup>
import { login, sendCode as sendCodeApi } from '@/api/auth'
const router = useRouter()
const route = useRoute()

const authStore = useAuthStore()

const {
  form,
  send: submit,
  loading: submitting,
} = useForm((data) => login(data), {
  initialForm: {
    phone: '17603013019',
    code: '',
  },
}).onSuccess(() => {
  // console.log(authStore.user.id)
  authStore.form = route.authStore
  router.replace('/')
})

const {
  send: sendCode,
  loading: sendingCode,
  countdown,
} = useCaptcha(
  () =>
    sendCodeApi({
      phone: form.value.phone,
    }),
  {
    immediate: false,
  },
).onSuccess(({ data }) => {
  if (data.code) {
    form.value.code = data.code
    submit()
  }
})
</script>

<style lang="scss" scoped></style>
