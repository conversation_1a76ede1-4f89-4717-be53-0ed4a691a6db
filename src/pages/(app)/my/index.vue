<template>
  <Page>
    <template #header>
      <van-nav-bar title="" safe-area-inset-top :border="false" class="bg-transparent!" />
    </template>
    <div>
      <van-button type="primary " @click="t()"> BTN </van-button>
    </div>
  </Page>
</template>

<script setup>
definePage({
  name: 'My',
  meta: {
    title: '我的',
    keepAlive: true,
  },
})
defineOptions({
  name: 'My',
})
const authStore = useAuthStore()
authStore.refresh()

async function t() {}
</script>

<style lang="scss" scoped>
.page {
  background-image: linear-gradient(to bottom, #5581f6 0, #f4f6fa 100%);
  background-size: 100% 606px;
  background-repeat: no-repeat;
}
</style>
