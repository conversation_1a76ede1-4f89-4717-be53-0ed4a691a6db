<template>
  <section class="page size-full bg-gray-100 flex flex-col" :class="{}">
    <header class="page__header flex-none">
      <slot name="header"></slot>
    </header>
    <main class="page__main flex-1 basis-0 overflow-y-scroll">
      <slot></slot>
    </main>
    <footer class="page__footer flex-none">
      <slot name="footer"></slot>
    </footer>
  </section>
</template>

<script setup>
const props = defineProps({})
</script>

<style lang="scss" scoped></style>
