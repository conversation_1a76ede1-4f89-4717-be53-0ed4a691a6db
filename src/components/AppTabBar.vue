<template>
  <AnimatePresence>
    <Motion
      v-if="visible"
      :initial="{
        y: 50,
      }"
      :animate="{
        y: 0,
      }"
      :exit="{
        y: 50,
      }"
      :transition="{
        type: 'tween',
      }"
      as-child
    >
      <van-tabbar
        ref="tabBer"
        :model-value="activeName"
        class="b-rounded-t-5 overflow-hidden shadow-xs z-10"
        @change="handleChange"
      >
        <van-tabbar-item v-for="item in data" :key="item.name" :name="item.name" class="">
          <span class="">
            {{ item.title }}
          </span>
          <template #icon="{ active }">
            <Motion
              as="img"
              :key="`${item.name}-${active}`"
              :src="active ? item.iconSrcSelected : item.iconSrc"
              :alt="item.title"
              :initial="
                active
                  ? {
                      scale: 0,
                    }
                  : false
              "
              :animate="{
                scale: 1,
              }"
            />
          </template>
        </van-tabbar-item>
      </van-tabbar>
    </Motion>
  </AnimatePresence>
</template>

<script setup>
import { Motion, AnimatePresence } from 'motion-v'
import { getNavMenuList } from '@/api/app'
const router = useRouter()
const route = useRoute()
const appStore = useAppStore()
const activeName = computed(() => route.path)
const tabBer = useTemplateRef('tabBer')
const { height } = useElementSize(() => unrefElement(tabBer))

const {
  data,
  loading,
  send: refresh,
} = useWatcher(() => getNavMenuList(), [() => appStore.channel], {
  immediate: true,
  initialData: [],
  async middleware(_, next) {
    const data = await next()
    data.forEach((v) => {
      v.name = v.url
    })
    return data
  },
})

function handleChange(name) {
  const item = data.value.find((v) => v.name === name)
  console.log(item)
  if (item.type === '1') {
    // 切换tab
    // active.value = name
    router.replace(item.url)
    return
  }
  if (item.type === '2') {
    location.assign(item.url)
    return
  }
  if (item.type === '4') {
    router.push(item.url)
    return
  }
}

const visible = computed(() =>
  data.value.some((item) => item.name === activeName.value && item.type === '1'),
)

defineExpose({ activeName, refresh, visible, data, loading, tabBer, height })
</script>

<style lang="scss" scoped></style>
