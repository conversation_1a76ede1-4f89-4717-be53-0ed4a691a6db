<template>
  <van-button
    type="primary"
    class="p-1! size-a! bg-transparent! b-none!"
    @click="customerServicePopup.reveal()"
  >
    <slot>
      <van-icon class="i-ep:service size-6.5 text-white!"></van-icon>
    </slot>
  </van-button>
  <CustomerServicePopup ref="customerServicePopup" />
</template>

<script setup>
const customerServicePopup = useTemplateRef('customerServicePopup')
</script>

<style lang="scss" scoped></style>
