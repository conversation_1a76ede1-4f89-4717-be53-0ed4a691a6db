<template>
  <van-popup
    :show="show"
    teleport="body"
    closeable
    round
    class="customer-service-popup w-70! bg-linear-to-b! from-primary to-gray-100 from-[-27,6%] to-28.27% h-55!"
    :style="{
      '--color-primary': 'red',
      '--van-primary-color': 'red',
    }"
    @click-close-icon="cancel()"
    @click-overlay="cancel()"
  >
    <header class="pt-5">
      <div class="title text-#2246B7 text-center text-lg font-semibold">联系客服</div>
      <div class="sub-title text-#2246B7/50 text-center pt-2">
        若您遇到问题，请尝试以下方式联系客服
      </div>
    </header>
    <main class="bg-white rounded p-4">
      <van-button type="primary" round block> 电话客服：{{ hotlinePhone }} </van-button>
      <van-button type="primary" round block>
        <van-icon class="i-ep:service size-6.5 text-white!"></van-icon>在线客服
      </van-button>
    </main>
  </van-popup>
</template>

<script setup>
import { getHotlinePhone } from '@/api/app'

const [show, toggleShow] = useToggle(false)
const { reveal, cancel, confirm, onReveal, onCancel, onConfirm } = useConfirmDialog()

const { data: hotlinePhone, send: refresh } = useRequest(() => getHotlinePhone(), {
  immediate: true,
}).onSuccess((data) => {
  console.log(data)
})

onReveal(() => {
  refresh()
  toggleShow(true)
})
onCancel(() => {
  toggleShow(false)
})
onConfirm(() => {
  toggleShow(false)
})
defineExpose({
  show,
  toggleShow,
  reveal,
  cancel,
})
</script>

<style lang="scss" scoped>
.customer-service-popup {
}
</style>
