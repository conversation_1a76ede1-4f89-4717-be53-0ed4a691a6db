<template>
  <van-popup
    :show="show"
    teleport="body"
    closeable
    class="customer-service-popup w-70"
    @click-close-icon="cancel()"
    @click-overlay="cancel()"
  >
    <header>
      <div class="title">联系客服</div>
      <div class="sub-title">若您遇到问题，请尝试以下方式联系客服</div>
    </header>
    <main>
      <van-button type="primary" round block> 电话客服：{{ hotlinePhone }} </van-button>
      <van-button type="primary" round block>
        <van-icon class="i-ep:service size-6.5 text-white!"></van-icon>在线客服
      </van-button>
    </main>
  </van-popup>
</template>

<script setup>
import { getHotlinePhone } from '@/api/app'

const [show, toggleShow] = useToggle(false)
const { reveal, cancel, confirm, onReveal, onCancel, onConfirm } = useConfirmDialog()

const { data: hotlinePhone, send: refresh } = useRequest(() => getHotlinePhone(), {
  immediate: true,
}).onSuccess((data) => {
  console.log(data)
})

onReveal(() => {
  refresh()
  toggleShow(true)
})
onCancel(() => {
  toggleShow(false)
})
onConfirm(() => {
  toggleShow(false)
})
defineExpose({
  show,
  toggleShow,
  reveal,
  cancel,
})
</script>

<style lang="scss" scoped>
.customer-service-popup {
}
</style>
