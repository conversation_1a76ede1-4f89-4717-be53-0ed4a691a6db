<template>
  <div
    :key="data.spuId"
    :data-spu-id="data.spuId"
    class="goods-list-item bg-white rounded-3 overflow-hidden"
  >
    <div class="p-1">
      <img :src="data.image" alt="" class="block w-full rounded-3" />
    </div>
    <div class="p-2">
      <div class="line-clamp-2">
        {{ data.spuTitle }}
      </div>
      <div class="">
        <span class="text-5">
          <sub class="bottom-0">￥</sub>
          <strong
            ><span>{{ data.priceDown.toString().split('.')[0] }}</span
            ><sub v-if="data.priceDown.toString().includes('.')" class="bottom-0"
              >.{{ data.priceDown.toString().split('.')[1] }}</sub
            ></strong
          >
        </span>
      </div>
    </div>
  </div>
</template>

<script setup>
defineOptions({
  name: 'GoodsListItem',
})
const props = defineProps({
  data: {
    type: Object,
    // default: () => [],
    required: true,
  },
})
</script>

<style lang="scss" scoped></style>
