<template>
  <div class="relative size-full transform-3d translate-z-0">
    <RouterView v-slot="{ Component, route }">
      <AnimatePresence mode="popLayout" :custom="direction">
        <Motion
          :key="route.path"
          class="page-animate-wrapper relative size-full"
          :variants="variants"
          initial="initial"
          animate="animate"
          exit="exit"
          :transition="{
            type: 'tween',
            // duration: 3,
          }"
        >
          <component :key="route.path" :is="genPageComponent(Component, route)" />
        </Motion>
      </AnimatePresence>
    </RouterView>
  </div>
</template>

<script setup>
import { KeepAlive } from 'vue'
import { cloneDeep } from 'lodash-es'
import { Motion, AnimatePresence, motion } from 'motion-v'
const router = useRouter()
const route = useRoute()
const currentPosition = ref(history.state?.position ?? 0)
const direction = ref()
const include = ref([])
onBeforeUnmount(
  router.afterEach((to, from) => {
    const position = history.state?.position ?? 0
    if (position > currentPosition.value) {
      direction.value = 'forward'
      currentPosition.value = position
      return
    }
    if (position < currentPosition.value) {
      direction.value = 'back'
      currentPosition.value = position
      return
    }
    direction.value = 'replace'
    currentPosition.value = position
    return
  }),
)

const variants = {
  initial(direction) {
    switch (direction) {
      case 'forward': {
        return {
          opacity: 1,
          x: '100%',
          zIndex: 1,
          scale: 1,
        }
      }
      case 'back': {
        return {
          opacity: 1,
          x: '-50%',
          zIndex: 0,
          scale: 1,
        }
      }
      case 'replace': {
        return {
          opacity: 0,
          x: 0,
          zIndex: 0,
          scale: 1,
        }
      }
    }
  },
  animate(direction) {
    switch (direction) {
      case 'forward': {
        return {
          opacity: 1,
          x: 0,
          zIndex: 0,
          scale: 1,
        }
      }
      case 'back': {
        return {
          opacity: 1,
          x: 0,
          zIndex: 0,
          scale: 1,
        }
      }
      case 'replace': {
        return {
          opacity: 1,
          x: 0,
          zIndex: 0,
          scale: 1,
        }
      }
    }
  },
  exit(direction) {
    switch (direction) {
      case 'forward': {
        return {
          opacity: 1,
          x: '-50%',
          zIndex: 0,
          scale: 1,
        }
      }
      case 'back': {
        return {
          opacity: 1,
          x: '100%',
          zIndex: 1,
          scale: 1,
        }
      }
      case 'replace': {
        return {
          opacity: 1.1, // 为了播放退出动画，避免直接退出
          x: 0,
          zIndex: 0,
          scale: 1,
        }
      }
    }
  },
}

watch(
  route,
  (route) => {
    const currentRoute = route.matched.find((v, i) => i > 0 && v.components)
    if (!currentRoute) return
    if (!currentRoute.meta.keepAlive) return
    const name = route.path
    if (!name) return
    if (include.value.includes(name)) return
    include.value.push(name)
  },
  {
    immediate: true,
  },
)

// function updateKeepAliveInclude(to) {
//   console.log(to)
//   const route = to.matched.find((v, i) => i > 0 && v.components)
//   if (!route) return
//   if (!route.meta.keepAlive) return
//   const name = route?.components?.default?.name
//   if (!name) return
//   if (include.value.includes(name)) return
//   include.value.push(name)
// }

// updateKeepAliveInclude(route)

// onBeforeUnmount(
//   router.beforeResolve((to, from) => {
//     updateKeepAliveInclude(to)
//   }),
// )

const map = new Map()

// 对组件改名，使页面组件名和路由对应实现按路由缓存
function genPageComponent(Component, route) {
  // console.log(Motion, motion)
  // console.log(map)
  // const name = 'route-' + route.path
  // if (map.has(name)) {
  //   return map.get(name)
  // }

  // // const name = route.path
  // // if (map.has(name)) {
  // //   return map.get(name)
  // // }

  // // Component.type.name = name
  // // const c = motion.create(Component)
  // const c = Component
  // c.name = name
  // map.set(name, c)

  // const type = map.get(name) ?? cloneDeep(Component.type)
  // type.name = name
  // Component.type = type
  // map.set(type.name, type)
  return Component
}
</script>

<style lang="scss" scoped></style>
