<template>
  <van-nav-bar
    safe-area-inset-top
    :border="false"
    class="home-header bg-transparent! [&_.van-nav-bar\_\_title]:(max-w-none w-full) [&_.van-nav-bar\_\_content]:h-a!"
  >
    <template #title>
      <div class="p-3">
        <div class="flex items-center">
          <img src="@/assets/appName.png" alt="" class="w-15 h-5.5 mr-2 block" />
          <div class="flex-1 bg-op-30 bg-white rounded-full h-9 flex items-center">
            <van-icon class="i-ep:service size-5 text-white! ml-2 mr-2"></van-icon>
            <div class="flex-1 text-white font-normal text-left">iphone特惠滚动</div>
            <van-button class="h-[7]! mr-[1]!" type="primary" round> 搜索 </van-button>
          </div>
          <CustomerServiceButton />
        </div>
        <div class="flex items-center justify-between text-white font-normal text-3 mt-1">
          <div
            v-for="item in [
              {
                icon: 'i-custom:zheng',
                label: '正品好物',
              },
              {
                icon: 'i-custom:yin',
                label: '隐私保护',
              },
              {
                icon: 'i-custom:shou',
                label: '极速送达',
              },
              {
                icon: 'i-custom:shou',
                label: '售后无忧',
              },
            ]"
            class="flex items-center"
          >
            <van-icon class="size-5 mr-1" :class="item.icon"></van-icon>
            <span>{{ item.label }}</span>
          </div>
        </div>
      </div>
    </template>
  </van-nav-bar>
</template>

<script setup></script>

<style lang="scss" scoped></style>
