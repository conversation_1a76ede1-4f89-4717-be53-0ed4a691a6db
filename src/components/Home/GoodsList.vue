<template>
  <div class="">
    <van-tabs
      v-model:active="active"
      background="transparent"
      line-width="0"
      line-height="0"
      title-active-color="red"
      class="[&_.van-tab]:(text-4 p-1)"
    >
      <van-tab
        v-for="item in categories"
        :key="item.name"
        :name="item.name"
        :title="item.title"
        class=""
      >
      </van-tab>
    </van-tabs>
    <van-list
      :loading="loading"
      :finished="isLastPage"
      finished-text="没有更多了"
      :immediate-check="false"
      @load="onLoad"
    >
      <div class="grid grid-cols-2 px-3 gap-2 items-start">
        <GoodsList :data="data.filter((v, i) => i % 2 === 0)" />
        <GoodsList :data="data.filter((v, i) => i % 2 === 1)" />
      </div>
    </van-list>
  </div>
</template>

<script setup>
defineOptions({
  name: 'HomeGoodsList',
})
import { getGoodsCategories, getGoodsList, getRegionGoodsList } from '@/api/goods'
import { cloneDeep, groupBy } from 'lodash-es'
import GoodsList from '@/components/GoodsList/index.vue'

const active = ref()

const { data: categories, send: refreshCategories } = useRequest(
  () =>
    getGoodsCategories({
      navStatus: 'Y',
      parentId: '0',
      querySub: 'Y',
    }),
  {
    initialData: [],
    async middleware(_, next) {
      const data = await next()
      // const map = new Map()
      // data.forEach((item) => {
      //   if (!map.has(item.categoryId)) {
      //     item.children = []
      //     map.set(item.categoryId, item)
      //   }
      //   if (!map.has(item.parentId)) {
      //     map.set(item.parentId, {
      //       categoryId: item.parentId,
      //       children: [],
      //     })
      //   }
      //   const parent = map.get(item.parentId)
      //   parent.children.push(item)
      // })

      return [
        {
          regionName: '热销榜单',
          type: 'region',
          regionType: 'FOLLOW',
        },
        ...data,
      ].map((v) => ({
        ...v,
        type: v.type ?? 'category',
        name: v.type === 'region' ? `region-${v.regionType}` : `category-${v.categoryId}`,
        title: v.type === 'region' ? v.regionName : v.categoryName,
      }))
    },
  },
).onSuccess(({ data }) => {
  if (!data.some((v) => v.name === active.value)) {
    active.value = data[0].name
  }
})

const activeItem = computed(() => categories.value.find((v) => v.name === active.value))

const { data, reload, page, isLastPage, loading, total } = usePagination(
  (pageNum, pageSize) =>
    activeItem.value.type === 'region'
      ? getRegionGoodsList({
          pageNum,
          pageSize,
          regionType: activeItem.value.regionType,
        })
      : getGoodsList({
          pageNum,
          pageSize,
          categoryId: activeItem.value.categoryId,
        }),
  {
    immediate: !!activeItem.value,
    append: true,
    watchingStates: [active],
    async middleware(_, next) {
      if (!activeItem.value) return
      return next()
    },
  },
)

function onLoad() {
  page.value++
}

async function refresh() {
  await refreshCategories()
  reload()
}

defineExpose({
  refresh,
})
</script>

<style lang="scss" scoped></style>
