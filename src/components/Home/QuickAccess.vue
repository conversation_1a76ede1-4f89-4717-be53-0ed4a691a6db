<template>
  <div class="home-quick-access grid grid-cols-5 gap-2">
    <van-button v-for="item in data" block class="p-0! bg-transparent! b-none! h-a!">
      <img :src="item.icon" alt="" class="size-10 block m-auto" />
      <div class="text-center mt-1">
        {{ item.name }}
      </div>
    </van-button>
  </div>
</template>

<script setup>
import { getFuncRegion } from '@/api/app'
const appStore = useAppStore()
const {
  data,
  send: refresh,
  loading,
} = useWatcher(
  () =>
    getFuncRegion({
      funcRegion: 'APP_HOME',
    }),
  [computed(() => appStore.channel)],
  {
    immediate: true,
  },
)
defineExpose({ refresh })
</script>

<style lang="scss" scoped></style>
