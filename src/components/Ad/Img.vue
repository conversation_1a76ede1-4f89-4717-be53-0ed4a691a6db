<template>
  <img
    v-if="data"
    :src="data.pic"
    :alt="data.name"
    :key="data.id"
    :data-id="data.id"
    @click="to()"
  />
</template>

<script setup>
defineOptions({
  name: 'AdImg',
})
const props = defineProps({
  data: {
    type: Object,
    // default:() => {},
    required: true,
  },
  autoTrackEvent: {
    type: Boolean,
    default: true,
  },
  beforeClick: {
    type: Function,
    default: () => () => true,
  },
})

async function to() {
  // 点击
  const v = await props.beforeClick()
  if (!v) return
  console.log(props.data)
}

whenever(
  () => props.data,
  (data) => {
    // 曝光
  },
  {
    immediate: true,
  },
)
onActivated(() => {
  // 曝光
})
onMounted(() => {})
</script>

<style lang="scss" scoped></style>
