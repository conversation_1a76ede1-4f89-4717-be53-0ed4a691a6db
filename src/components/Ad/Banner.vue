<template>
  <van-swipe class="ad-banner">
    <van-swipe-item v-for="item in data">
      <AdImg :data="item" class="block w-full h-a" />
      <!-- {{ appStore.channel }}
      {{ item.appVersion }} -->
    </van-swipe-item>
  </van-swipe>
</template>

<script setup>
import { getAdvertisement } from '@/api/app'

const props = defineProps({
  regionType: {
    type: String,
    default: '',
    required: true,
  },
})

const appStore = useAppStore()
const authStore = useAuthStore()

const {
  data,
  send: refresh,
  loading,
} = useWatcher(
  () => {
    const m = getAdvertisement({
      regionType: [props.regionType],
      // appVersion: appStore.channel,
    })
    m.config.transform = (data) => {
      return data?.[props.regionType] ?? []
    }
    return m
  },
  [() => props.regionType, () => appStore.channel],
  {
    initialData: [],
    immediate: true,
    // async middleware(_, next) {
    //   const data = await next()
    //   return data?.[props.regionType] ?? []
    // },
    // debounce: 0,
    // force: true,
    // shareRequest: false,
  },
)

defineExpose({ refresh })
</script>

<style lang="scss" scoped></style>
