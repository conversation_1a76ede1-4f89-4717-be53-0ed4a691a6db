<template>
  <div v-if="data.length" ref="swiperContainer" class="ad-kebab swiper">
    <div class="swiper-wrapper">
      <div
        v-for="(item, index) in data"
        :key="index"
        :data-id="item.id"
        :data-index="index"
        class="swiper-slide w-a!"
      >
        <AdImg
          :data="item"
          class="block w-a"
          :class="{
            'h-30': index % 2,
            'h-27 mt-1.5': !(index % 2),
          }"
        />
      </div>
    </div>
  </div>
</template>

<script setup>
import { getAdvertisement } from '@/api/app'
import Swiper from 'swiper'
import { Navigation, Pagination, EffectCoverflow, Autoplay } from 'swiper/modules'
import 'swiper/css'
import 'swiper/css/pagination'

const props = defineProps({
  regionType: {
    type: String,
    default: '',
    required: true,
  },
})

const swiperContainer = useTemplateRef('swiperContainer')
const visibleIds = ref([])
function updateVisibleIds(swiper) {
  const ids = swiper?.visibleSlides?.map((slide) => +slide.dataset.id) ?? []
  visibleIds.value = ids
}

whenever(swiperContainer, (el) => {
  console.log(el)
  const modifier = window.innerWidth / 375
  const swiper = new Swiper(el, {
    modules: [Navigation, Pagination, EffectCoverflow, Autoplay],
    slidesPerView: 'auto',
    spaceBetween: 8 * modifier,
    loop: true,
    // loopAdditionalSlides: 1,
    direction: 'horizontal',
    speed: 800,
    watchSlidesProgress: true,
    slidesOffsetBefore: 10 * modifier,
    slidesOffsetAfter: 10 * modifier,
    autoplay: {
      delay: 5000,
      disableOnInteraction: false,
    },
    on: {
      init(swiper) {
        updateVisibleIds(swiper)
      },
      realIndexChange: (swiper) => {
        updateVisibleIds(swiper)
      },
    },
  })

  onWatcherCleanup(
    watch(
      data,
      () => {
        swiper.update()
      },
      {
        deep: true,
        flush: 'post',
      },
    ),
  )
  onWatcherCleanup(() => {
    swiper.destroy()
  })
})

watchArray(visibleIds, (newList, oldList, added, removed) => {
  added.forEach((id) => {
    const ad = data.value.find((item) => item.id === id)
    if (!ad) return
    // addAdEventRecord(ad, 'EXPOSURE')
  })
})

const appStore = useAppStore()
const authStore = useAuthStore()

const {
  data,
  send: refresh,
  loading,
} = useWatcher(
  () =>
    getAdvertisement({
      regionType: [props.regionType],
      // appVersion: appStore.channel,
    }),
  [() => props.regionType, computed(() => appStore.channel)],
  {
    initialData: [],
    immediate: true,
    async middleware(_, next) {
      const data = await next()
      return data?.[props.regionType] ?? []
    },
    // debounce: 0,
    // force: true,
    // shareRequest: false,
  },
)

defineExpose({ refresh })
</script>

<style lang="scss" scoped></style>
